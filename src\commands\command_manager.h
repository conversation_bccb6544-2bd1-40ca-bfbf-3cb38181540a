#pragma once

#include <memory>
#include <unordered_map>
#include <functional>
#include <CLI/CLI.hpp>

#include "base_command.h"

namespace sco {

class CommandManager {
public:
    CommandManager();
    ~CommandManager() = default;
    
    void register_commands(CLI::App& app);
    int execute();
    
private:
    std::unordered_map<std::string, std::unique_ptr<BaseCommand>> commands_;
    std::function<int()> current_command_;
    
    void register_command(const std::string& name, std::unique_ptr<BaseCommand> command);
    
    // Command registration helpers
    void register_install_command(CLI::App& app);
    void register_uninstall_command(CLI::App& app);
    void register_update_command(CLI::App& app);
    void register_search_command(CLI::App& app);
    void register_list_command(CLI::App& app);
    void register_info_command(CLI::App& app);
    void register_home_command(CLI::App& app);
    void register_bucket_command(CLI::App& app);
    void register_config_command(CLI::App& app);
    void register_cache_command(CLI::App& app);
    void register_help_command(CLI::App& app);
    void register_cat_command(CLI::App& app);
    void register_checkup_command(CLI::App& app);
    void register_cleanup_command(CLI::App& app);
    void register_create_command(CLI::App& app);
    void register_depends_command(CLI::App& app);
    void register_download_command(CLI::App& app);
    void register_export_command(CLI::App& app);
    void register_import_command(CLI::App& app);
    void register_hold_command(CLI::App& app);
    void register_unhold_command(CLI::App& app);
    void register_prefix_command(CLI::App& app);
    void register_reset_command(CLI::App& app);
    void register_shim_command(CLI::App& app);
    void register_status_command(CLI::App& app);
    void register_virustotal_command(CLI::App& app);
    void register_which_command(CLI::App& app);
    void register_alias_command(CLI::App& app);
};

} // namespace sco
