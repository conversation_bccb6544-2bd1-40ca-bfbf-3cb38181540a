#pragma once

#include "base_command.hpp"
#include "../core/config.hpp"
#include <iostream>
#include <spdlog/spdlog.h>

namespace sco {

class ConfigCommand : public BaseCommand {
public:
    ConfigCommand() = default;
    
    int execute() override {
        try {
            auto& config = Config::instance();
            config.load();
            
            if (key_.empty()) {
                // Show all configuration
                show_all_config();
            } else if (value_.empty()) {
                // Get specific configuration
                show_config_value(key_);
            } else {
                // Set configuration
                set_config_value(key_, value_);
            }
            
            return 0;
        } catch (const std::exception& e) {
            spdlog::error("Config command failed: {}", e.what());
            return 1;
        }
    }
    
    std::string get_name() const override { return "config"; }
    std::string get_description() const override { return "Get or set configuration values"; }
    
    void set_key(const std::string& key) { key_ = key; }
    void set_value(const std::string& value) { value_ = value; }
    
private:
    std::string key_;
    std::string value_;
    
    void show_all_config() {
        auto& config = Config::instance();

        SPDLOG_INFO("Current configuration:");
        SPDLOG_INFO("  aria2-enabled: {}", config.get_bool("aria2-enabled") ? "true" : "false");
        SPDLOG_INFO("  aria2-warning-enabled: {}", config.get_bool("aria2-warning-enabled") ? "true" : "false");
        SPDLOG_INFO("  aria2-retry-wait: {}", config.get_int("aria2-retry-wait"));
        SPDLOG_INFO("  aria2-split: {}", config.get_int("aria2-split"));
        SPDLOG_INFO("  aria2-max-connection-per-server: {}", config.get_int("aria2-max-connection-per-server"));
        SPDLOG_INFO("  aria2-min-split-size: {}", config.get("aria2-min-split-size"));
        SPDLOG_INFO("  aria2-options: {}", config.get("aria2-options"));
        SPDLOG_INFO("  debug: {}", config.get_bool("debug") ? "true" : "false");
        SPDLOG_INFO("  force_update: {}", config.get_bool("force_update") ? "true" : "false");
        SPDLOG_INFO("  show_update_log: {}", config.get_bool("show_update_log") ? "true" : "false");
        SPDLOG_INFO("  scoop_repo: {}", config.get("scoop_repo"));
        SPDLOG_INFO("  scoop_branch: {}", config.get("scoop_branch"));

        SPDLOG_INFO("");
        SPDLOG_INFO("Paths:");
        SPDLOG_INFO("  scoop_dir: {}", config.get_scoop_dir().string());
        SPDLOG_INFO("  apps_dir: {}", config.get_apps_dir().string());
        SPDLOG_INFO("  cache_dir: {}", config.get_cache_dir().string());
        SPDLOG_INFO("  buckets_dir: {}", config.get_buckets_dir().string());
        SPDLOG_INFO("  shims_dir: {}", config.get_shims_dir().string());
    }
    
    void show_config_value(const std::string& key) {
        auto& config = Config::instance();

        // Check if it's a boolean config
        if (key == "aria2-enabled" || key == "aria2-warning-enabled" ||
            key == "debug" || key == "force_update" || key == "show_update_log") {
            SPDLOG_INFO("{}", config.get_bool(key) ? "true" : "false");
        }
        // Check if it's an integer config
        else if (key == "aria2-retry-wait" || key == "aria2-split" ||
                 key == "aria2-max-connection-per-server") {
            SPDLOG_INFO("{}", config.get_int(key));
        }
        // String config
        else {
            std::string value = config.get(key);
            if (value.empty()) {
                SPDLOG_WARN("Configuration key '{}' not found", key);
                return;
            }
            SPDLOG_INFO("{}", value);
        }
    }
    
    void set_config_value(const std::string& key, const std::string& value) {
        auto& config = Config::instance();

        // Check if it's a boolean config
        if (key == "aria2-enabled" || key == "aria2-warning-enabled" ||
            key == "debug" || key == "force_update" || key == "show_update_log") {
            bool bool_value = (value == "true" || value == "1" || value == "yes");
            config.set_bool(key, bool_value);
            SPDLOG_INFO("Set {} to {}", key, bool_value ? "true" : "false");
        }
        // Check if it's an integer config
        else if (key == "aria2-retry-wait" || key == "aria2-split" ||
                 key == "aria2-max-connection-per-server") {
            try {
                int int_value = std::stoi(value);
                config.set_int(key, int_value);
                SPDLOG_INFO("Set {} to {}", key, int_value);
            } catch (const std::exception&) {
                SPDLOG_ERROR("Invalid integer value: {}", value);
                return;
            }
        }
        // String config
        else {
            config.set(key, value);
            SPDLOG_INFO("Set {} to {}", key, value);
        }

        config.save();
        SPDLOG_DEBUG("Configuration updated: {} = {}", key, value);
    }
};

} // namespace sco
