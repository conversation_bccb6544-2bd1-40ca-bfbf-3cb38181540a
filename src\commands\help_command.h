#pragma once

#include "base_command.h"
#include <CLI/CLI.hpp>

namespace sco {

class HelpCommand : public BaseCommand {
public:
    HelpCommand() = default;
    
    int execute() override;
    std::string get_name() const override { return "help"; }
    std::string get_description() const override { return "Show help for a command"; }
    
    void set_command_name(const std::string& name) { command_name_ = name; }
    void set_app(CLI::App* app) { app_ = app; }
    
private:
    std::string command_name_;
    CLI::App* app_ = nullptr;
};

} // namespace sco
