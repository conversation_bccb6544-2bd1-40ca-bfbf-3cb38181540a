#include <iostream>
#include <string>
#include <vector>
#include <CLI/CLI.hpp>
#include <spdlog/spdlog.h>

#include "commands/command_manager.hpp"
#include "core/config.hpp"
#include "core/version.h"

int main(int argc, char** argv) {
    try {
        CLI::App app{"sco - A command-line installer for Windows", "sco"};
        app.set_version_flag("--version,-v", SCO_VERSION);
        
        // Global options
        bool verbose = false;
        bool quiet = false;
        bool global = false;
        
        app.add_flag("--verbose", verbose, "Enable verbose output");
        app.add_flag("--quiet,-q", quiet, "Enable quiet mode");
        app.add_flag("--global,-g", global, "Apply to global installation");
        
        // Initialize command manager
        sco::CommandManager cmd_manager;
        
        // Register all commands
        cmd_manager.register_commands(app);
        
        // Parse command line
        CLI11_PARSE(app, argc, argv);
        
        // Set logging level based on flags
        if (quiet) {
            spdlog::set_level(spdlog::level::warn);
        } else if (verbose) {
            spdlog::set_level(spdlog::level::debug);
        } else {
            spdlog::set_level(spdlog::level::info);
        }
        
        // Set global flag in config
        if (global) {
            sco::Config::instance().set_global_mode(true);
        }
        
        // Execute the command
        return cmd_manager.execute();
        
    } catch (const std::exception& e) {
        spdlog::error("Error: {}", e.what());
        return 1;
    }
}
