#include "src/utils/table_formatter.hpp"
#include <iostream>

using namespace sco;

int main() {
    std::cout << "=== 制表符对齐问题演示 ===\n\n";
    
    // 模拟制表符对齐的问题
    std::cout << "使用制表符的问题:\n";
    std::cout << "Name\t\t\tVersion\t\tSource\n";
    std::cout << "----\t\t\t-------\t\t------\n";
    std::cout << "git\t\t\t2.42.0\t\tmain\n";
    std::cout << "very-long-application-name\t1.0.0\t\tspc\n";  // 对齐被破坏
    std::cout << "vim\t\t\t9.0\t\tmain\n";
    
    std::cout << "\n=== TableFormatter 解决方案 ===\n\n";
    
    // 使用 TableFormatter 的完美对齐
    TableFormatter table;
    
    // 定义列
    table.add_column("Name", 20, true);      // 左对齐
    table.add_column("Version", 15, true);   // 左对齐
    table.add_column("Source", 8, true);     // 左对齐
    table.add_column("Size", 10, false);     // 右对齐 (数字)
    
    // 添加测试数据
    table.add_row({"git", "2.42.0", "main", "45.2MB"});
    table.add_row({"very-long-application-name", "1.0.0", "spc", "1.2GB"});
    table.add_row({"vim", "9.0", "main", "15MB"});
    table.add_row({"7zip", "24.09", "main", "2.1MB"});
    table.add_row({"everything", "1.4.1.1027", "extras", "3.5MB"});
    
    // 自动调整宽度并打印
    table.auto_adjust_widths();
    table.print();
    
    std::cout << "\n=== 不同分隔符样式 ===\n\n";
    
    // 使用等号分隔符
    table.print_with_separator('=');
    
    std::cout << "\n=== 数字右对齐演示 ===\n\n";
    
    TableFormatter stats_table;
    stats_table.add_column("Category", 15, true);   // 左对齐
    stats_table.add_column("Count", 8, false);      // 右对齐
    stats_table.add_column("Size", 12, false);      // 右对齐
    
    stats_table.add_row({"Apps", "25", "1.2GB"});
    stats_table.add_row({"Buckets", "3", "15MB"});
    stats_table.add_row({"Cache Files", "150", "500MB"});
    
    stats_table.auto_adjust_widths();
    stats_table.print();
    
    return 0;
}
