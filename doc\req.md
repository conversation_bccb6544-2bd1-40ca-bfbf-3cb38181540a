# sco C++ 重写项目

## 项目概述

这是一个命令行工具，用于Windows系统下的软件包管理。该项目计划使用C++重写[ScoopInstaller/Scoop](https://github.com/ScoopInstaller/Scoop)的所有功能，旨在提供更高性能、更可靠的Windows包管理体验。

## 核心设计理念

- **无UAC提示**: 用户级安装，避免管理员权限要求
- **环境变量管理**: 自动管理PATH，避免环境变量污染
- **便携式应用优先**: 支持便携式应用的安装和管理
- **依赖自动解析**: 自动处理软件包依赖关系
- **脚本友好**: 支持自动化脚本和批量操作

## 详细功能需求

### 1. 核心包管理功能

#### 1.1 包安装功能 (install)
- 从指定源下载软件包
- 解压和安装软件包到指定目录 (`~/scoop/apps/` 或全局目录)
- 处理依赖关系自动安装
- 支持多连接下载 (aria2 集成)
- 处理安装前后脚本执行
- 支持全局安装 (`--global` 参数)
- 支持指定版本安装
- 创建 shims 和快捷方式

#### 1.2 包卸载功能 (uninstall)
- 完全移除已安装的软件包
- 清理相关文件和目录
- 处理卸载脚本
- 移除 shims 和快捷方式
- 保持系统清洁，无残留文件
- 支持强制卸载选项

#### 1.3 包更新功能 (update)
- 检查已安装包的新版本
- 更新单个或所有包 (`--all` 参数)
- 保持用户配置和数据
- 处理更新脚本
- 支持跳过特定包的更新
- 显示更新摘要

#### 1.4 包搜索功能 (search)
- 在所有已知 bucket 中搜索包
- 支持模糊匹配和正则表达式
- 显示包的基本信息和描述
- 支持按类别筛选
- 高亮显示搜索关键词

#### 1.5 包列表功能 (list)
- 列出所有已安装的包
- 显示包版本信息
- 支持过滤和排序
- 显示安装状态（本地/全局）
- 支持输出格式选择（表格/JSON）

### 2. Bucket 管理功能

#### 2.1 Bucket 操作 (bucket)
- `bucket add <name> [<repo>]`: 添加新的软件源
- `bucket remove <name>`: 移除软件源
- `bucket list`: 列出已添加的 bucket
- `bucket known`: 列出所有已知 bucket
- 自动更新 bucket 内容
- 验证 bucket 有效性

### 3. 配置管理功能

#### 3.1 配置管理 (config)
- 设置和获取配置选项
- 支持 aria2 相关配置:
  - `aria2-enabled` (默认: true)
  - `aria2-warning-enabled` (默认: true)
  - `aria2-retry-wait` (默认: 2)
  - `aria2-split` (默认: 5)
  - `aria2-max-connection-per-server` (默认: 5)
  - `aria2-min-split-size` (默认: 5M)
- 代理设置支持
- 安装路径配置
- 配置文件导入/导出

### 4. 信息查询功能

#### 4.1 包信息查询 (info)
- 显示包的详细信息
- 显示依赖关系
- 显示安装状态
- 显示版本历史
- 显示 manifest 内容

#### 4.2 主页打开 (home)
- 在默认浏览器中打开包的主页
- 支持批量打开多个包的主页

#### 4.3 Manifest 查看 (cat)
- 显示指定 manifest 的完整内容
- 支持语法高亮显示
- 支持格式化输出

#### 4.4 依赖关系查询 (depends)
- 列出应用的所有依赖关系
- 按安装顺序显示依赖
- 显示依赖关系树结构
- 检测循环依赖

#### 4.5 应用路径查询 (prefix)
- 返回指定应用的安装路径
- 支持相对路径和绝对路径输出

#### 4.6 可执行文件定位 (which)
- 定位 shim 或可执行文件的实际路径
- 类似于 Linux 的 which 命令
- 显示命令解析路径

### 5. 系统功能

#### 5.1 环境变量管理
- 自动管理 PATH 环境变量
- 避免环境变量污染
- 支持 shims 机制
- 自动创建和清理 shims
- 支持环境变量持久化

#### 5.2 权限管理
- 避免 UAC 提示
- 用户级安装，无需管理员权限
- 支持全局安装（需要管理员权限）

#### 5.3 缓存管理 (cache)
- `cache show`: 显示缓存使用情况
- `cache rm <app>`: 清理特定应用缓存
- `cache rm *`: 清理所有缓存文件
- 自动缓存清理策略

#### 5.4 系统检查 (checkup)
- 检查 Scoop 安装的潜在问题
- 验证环境变量配置
- 检查权限设置
- 检查依赖完整性
- 提供修复建议

#### 5.5 应用清理 (cleanup)
- 删除应用的旧版本
- 释放磁盘空间
- 支持全局清理 (`--global`)
- 支持清理所有应用 (`--all`)
- 保留指定数量的版本

#### 5.6 应用重置 (reset)
- 重置应用以解决冲突
- 重新创建 shims
- 修复环境变量
- 恢复默认配置

### 6. 高级功能

#### 6.1 依赖解析
- 自动解析和安装依赖包
- 处理依赖冲突
- 支持可选依赖
- 依赖关系图显示

#### 6.2 多版本支持
- 支持同时安装多个版本
- 版本切换功能 (`switch` 命令)
- 版本锁定功能

#### 6.3 脚本支持
- 支持 PowerShell 脚本执行
- 安装前后钩子脚本
- 自定义安装逻辑
- 脚本安全验证

#### 6.4 Manifest 处理
- 解析 JSON manifest 文件
- 验证 manifest 格式
- 支持 manifest 模板
- Manifest 语法检查

#### 6.5 应用版本管理
- 版本锁定功能 (`hold` 命令)
- 解锁版本更新 (`unhold` 命令)
- 显示锁定状态
- 批量版本管理

#### 6.6 状态监控 (status)
- 显示已安装应用状态
- 检查可用更新
- 显示过期应用
- 系统健康状态检查

#### 6.7 下载管理 (download)
- 预下载应用到缓存
- 验证文件哈希
- 支持批量下载
- 断点续传支持

#### 6.8 导入导出功能
- `export`: 导出已安装应用列表为 JSON
- `import`: 从 JSON 文件导入应用配置
- 支持配置迁移
- 批量安装支持

#### 6.9 Manifest 创建 (create)
- 创建自定义应用 manifest
- 提供 manifest 模板
- 交互式 manifest 生成
- 验证生成的 manifest

#### 6.10 Shim 管理 (shim)
- 创建和删除 shims
- 修复损坏的 shims
- 列出所有 shims
- 自定义 shim 行为

#### 6.11 别名管理 (alias)
- 创建命令别名
- 删除别名
- 列出所有别名
- 别名冲突检测

#### 6.12 安全检查 (virustotal)
- 在 VirusTotal 上检查应用哈希
- 检查下载 URL 安全性
- 显示安全扫描结果
- 集成安全警告

### 7. 用户体验功能

#### 7.1 进度显示
- 下载进度条
- 安装进度提示
- 详细的操作日志
- 彩色输出支持

#### 7.2 错误处理
- 友好的错误信息
- 操作回滚机制
- 详细的调试信息
- 错误代码标准化

#### 7.3 帮助系统
- 命令行帮助 (`--help`)
- 详细的使用文档
- 示例命令展示
- 在线文档链接

### 8. 技术特性

#### 8.1 性能优化
- 多线程下载
- 并行安装处理
- 内存优化
- 磁盘I/O优化

#### 8.2 跨平台兼容
- Windows 10/11 支持
- 不同架构支持 (x64, ARM64)
- PowerShell Core 兼容

#### 8.3 安全性
- 包完整性验证
- 数字签名检查
- 安全的下载机制
- 沙箱执行环境

## 技术栈

### 核心依赖库
- **CLI11**: 命令行参数解析
- **nlohmann_json**: JSON 处理
- **cxxopts**: 额外的命令行选项支持
- **spdlog**: 结构化日志系统

### 开发标准
- C++17 标准
- CMake 构建系统
- 跨平台兼容设计
- 单元测试覆盖

## 命令行接口设计

### 基本命令格式
```
sco <command> [<args>] [options]
```

### 主要命令列表
- `alias` - 管理 scoop 别名
- `bucket` - 管理 Scoop buckets
- `cache` - 显示或清理下载缓存
- `cat` - 显示指定 manifest 的内容
- `checkup` - 检查潜在问题
- `cleanup` - 通过删除旧版本清理应用
- `config` - 获取或设置配置值
- `create` - 创建自定义应用 manifest
- `depends` - 列出应用的依赖关系，按安装顺序
- `download` - 在缓存文件夹中下载应用并验证哈希
- `export` - 以 JSON 格式导出已安装的应用、buckets（可选配置）
- `help` - 显示命令帮助
- `hold` - 锁定应用以禁用更新
- `home` - 打开应用主页
- `import` - 从 JSON 格式的 Scoopfile 导入应用、buckets 和配置
- `info` - 显示应用信息
- `install` - 安装应用
- `list` - 列出已安装应用
- `prefix` - 返回指定应用的路径
- `reset` - 重置应用以解决冲突
- `search` - 搜索可用应用
- `shim` - 操作 Scoop shims
- `status` - 显示状态并检查新应用版本
- `unhold` - 解锁应用以启用更新
- `uninstall` - 卸载应用
- `update` - 更新应用或 Scoop 本身
- `virustotal` - 在 virustotal.com 上查找应用的哈希或 URL
- `which` - 定位 shim/可执行文件（类似于 Linux 上的 'which'）

### 全局选项
- `--global` - 全局安装
- `--verbose` - 详细输出
- `--quiet` - 静默模式
- `--help` - 显示帮助
- `--version` - 显示版本

## 兼容性要求

### 与原版 Scoop 兼容
- 完全兼容现有 manifest 格式
- 兼容现有 bucket 结构
- 兼容现有配置文件
- 支持现有安装目录结构

### 迁移支持
- 从 PowerShell 版本平滑迁移
- 保持现有安装的应用
- 配置自动迁移工具

## 性能目标

- 启动时间 < 100ms
- 包搜索响应时间 < 500ms
- 下载速度提升 50%+
- 内存使用优化 30%+

## 项目里程碑

### Phase 1: 核心功能
- [ ] 基础命令行框架
- [ ] JSON manifest 解析
- [ ] 基本的 install/uninstall 功能
- [ ] 配置系统

### Phase 2: 高级功能
- [ ] Bucket 管理
- [ ] 依赖解析
- [ ] 多线程下载
- [ ] 缓存系统

### Phase 3: 用户体验
- [ ] 进度显示
- [ ] 错误处理优化
- [ ] 帮助系统完善
- [ ] 性能优化

### Phase 4: 兼容性和发布
- [ ] 与原版 Scoop 完全兼容测试
- [ ] 迁移工具开发
- [ ] 文档完善
- [ ] 正式发布
