{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": "Build/msvc-debug/CMakeFiles/3.30.1/CMakeSystem.cmake"}, {"isExternal": true, "path": "C:/Local/vcpkg-latest/scripts/buildsystems/vcpkg.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDependentOption.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Platform/Windows-Initialize.cmake"}, {"isGenerated": true, "path": "Build/msvc-debug/CMakeFiles/3.30.1/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Platform/Windows.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Platform/WindowsPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Compiler/MSVC-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Compiler/MSVC.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Platform/Windows-MSVC-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Platform/Windows-MSVC.cmake"}, {"isGenerated": true, "path": "Build/msvc-debug/CMakeFiles/3.30.1/CMakeRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeRCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeCommonLanguageInclude.cmake"}, {"isExternal": true, "path": "C:/Local/vcpkg-latest/installed/x64-windows/share/cli11/CLI11ConfigVersion.cmake"}, {"isExternal": true, "path": "C:/Local/vcpkg-latest/installed/x64-windows/share/cli11/CLI11Config.cmake"}, {"isExternal": true, "path": "C:/Local/vcpkg-latest/installed/x64-windows/share/cli11/CLI11Config-debug.cmake"}, {"isExternal": true, "path": "C:/Local/vcpkg-latest/installed/x64-windows/share/cli11/CLI11Config-release.cmake"}, {"isExternal": true, "path": "C:/Local/vcpkg-latest/installed/x64-windows/share/nlohmann_json/nlohmann_jsonConfigVersion.cmake"}, {"isExternal": true, "path": "C:/Local/vcpkg-latest/installed/x64-windows/share/nlohmann_json/nlohmann_jsonConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "C:/Local/vcpkg-latest/installed/x64-windows/share/nlohmann_json/nlohmann_jsonTargets.cmake"}, {"isExternal": true, "path": "C:/Local/vcpkg-latest/installed/x64-windows/share/cxxopts/cxxopts-config-version.cmake"}, {"isExternal": true, "path": "C:/Local/vcpkg-latest/installed/x64-windows/share/cxxopts/cxxopts-config.cmake"}, {"isExternal": true, "path": "C:/Local/vcpkg-latest/installed/x64-windows/share/cxxopts/cxxopts-targets.cmake"}, {"isExternal": true, "path": "C:/Local/vcpkg-latest/installed/x64-windows/share/spdlog/spdlogConfigVersion.cmake"}, {"isExternal": true, "path": "C:/Local/vcpkg-latest/installed/x64-windows/share/spdlog/spdlogConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/FindThreads.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CheckLibraryExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CheckIncludeFileCXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CheckCXXSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Internal/CheckSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "C:/Local/vcpkg-latest/installed/x64-windows/share/fmt/fmt-config-version.cmake"}, {"isExternal": true, "path": "C:/Local/vcpkg-latest/installed/x64-windows/share/fmt/fmt-config.cmake"}, {"isExternal": true, "path": "C:/Local/vcpkg-latest/installed/x64-windows/share/fmt/fmt-targets.cmake"}, {"isExternal": true, "path": "C:/Local/vcpkg-latest/installed/x64-windows/share/fmt/fmt-targets-debug.cmake"}, {"isExternal": true, "path": "C:/Local/vcpkg-latest/installed/x64-windows/share/fmt/fmt-targets-release.cmake"}, {"isExternal": true, "path": "C:/Local/vcpkg-latest/installed/x64-windows/share/spdlog/spdlogConfigTargets.cmake"}, {"isExternal": true, "path": "C:/Local/vcpkg-latest/installed/x64-windows/share/spdlog/spdlogConfigTargets-debug.cmake"}, {"isExternal": true, "path": "C:/Local/vcpkg-latest/installed/x64-windows/share/spdlog/spdlogConfigTargets-release.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/GNUInstallDirs.cmake"}], "kind": "cmakeFiles", "paths": {"build": "C:/Code/scoop/Build/msvc-debug", "source": "C:/Code/scoop"}, "version": {"major": 1, "minor": 1}}