#pragma once

#include "base_command.hpp"
#include "../core/config.hpp"
#include <iostream>
#include <filesystem>
#include <fstream>
#include <spdlog/spdlog.h>

namespace sco {

class ListCommand : public BaseCommand {
public:
    ListCommand() = default;
    
    int execute() override {
        try {
            list_installed_apps();
            return 0;
        } catch (const std::exception& e) {
            spdlog::error("Failed to list apps: {}", e.what());
            return 1;
        }
    }
    
    std::string get_name() const override { return "list"; }
    std::string get_description() const override { return "List installed apps"; }
    
private:
    void list_installed_apps() {
        auto& config = Config::instance();
        auto apps_dir = config.get_apps_dir();
        
        if (!std::filesystem::exists(apps_dir)) {
            std::cout << "No apps installed.\n";
            return;
        }
        
        std::cout << "Installed apps:\n";
        
        try {
            for (const auto& entry : std::filesystem::directory_iterator(apps_dir)) {
                if (entry.is_directory()) {
                    std::string app_name = entry.path().filename().string();
                    
                    // Skip scoop itself
                    if (app_name == "scoop") {
                        continue;
                    }
                    
                    // Find current version
                    auto app_path = entry.path();
                    std::string current_version = "unknown";
                    
                    // Look for current.txt or check for version directories
                    auto current_file = app_path / "current.txt";
                    if (std::filesystem::exists(current_file)) {
                        std::ifstream file(current_file);
                        if (file.is_open()) {
                            std::getline(file, current_version);
                        }
                    } else {
                        // Check for version directories
                        for (const auto& version_entry : std::filesystem::directory_iterator(app_path)) {
                            if (version_entry.is_directory()) {
                                current_version = version_entry.path().filename().string();
                                break; // Take the first one found
                            }
                        }
                    }
                    
                    std::cout << "  " << app_name << " (" << current_version << ")\n";
                }
            }
        } catch (const std::filesystem::filesystem_error& e) {
            spdlog::error("Filesystem error: {}", e.what());
            throw;
        }
    }
};

} // namespace sco
