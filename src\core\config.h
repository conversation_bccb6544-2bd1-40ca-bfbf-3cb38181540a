#pragma once

#include <string>
#include <unordered_map>
#include <filesystem>
#include <nlohmann/json.hpp>

namespace sco {

class Config {
public:
    static Config& instance();
    
    // Configuration management
    void load();
    void save();
    
    // Getters
    std::string get(const std::string& key, const std::string& default_value = "") const;
    bool get_bool(const std::string& key, bool default_value = false) const;
    int get_int(const std::string& key, int default_value = 0) const;
    
    // Setters
    void set(const std::string& key, const std::string& value);
    void set_bool(const std::string& key, bool value);
    void set_int(const std::string& key, int value);
    
    // Global mode
    void set_global_mode(bool global) { global_mode_ = global; }
    bool is_global_mode() const { return global_mode_; }
    
    // Paths
    std::filesystem::path get_scoop_dir() const;
    std::filesystem::path get_apps_dir() const;
    std::filesystem::path get_cache_dir() const;
    std::filesystem::path get_buckets_dir() const;
    std::filesystem::path get_shims_dir() const;
    std::filesystem::path get_config_file() const;
    
    // Default configuration values
    void set_defaults();

private:
    Config() = default;
    ~Config() = default;
    Config(const Config&) = delete;
    Config& operator=(const Config&) = delete;
    
    nlohmann::json config_data_;
    bool global_mode_ = false;
    
    std::filesystem::path get_user_profile() const;
    std::filesystem::path get_program_data() const;
};

} // namespace sco
