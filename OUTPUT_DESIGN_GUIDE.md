# 输出设计指南

## 问题分析

之前的做法将所有 `std::cout` 替换为 spdlog 宏是不正确的。这混淆了**程序输出**和**日志信息**的概念。

## 正确的设计原则

### 📤 程序输出 (使用 std::cout/std::cerr)

**定义**: 用户期望的程序功能输出，是命令的直接结果

**使用场景**:
- ✅ 命令结果数据 (如 `list` 显示已安装应用)
- ✅ 配置查询结果 (如 `config key` 返回配置值)
- ✅ 帮助文档 (如 `help` 命令输出)
- ✅ 状态报告 (如 `status` 命令的主要输出)
- ✅ 缓存内容显示 (如 `cache show`)

**特点**:
- 用户直接消费的信息
- 可以被管道重定向 (`sco list > apps.txt`)
- 不应受日志级别影响
- 格式稳定，便于脚本解析

### 📝 日志信息 (使用 spdlog 宏)

**定义**: 程序运行时的状态、调试、错误信息

**使用场景**:
- ✅ 调试信息 (`SPDLOG_DEBUG`)
- ✅ 操作进度 (`SPDLOG_INFO`)
- ✅ 警告信息 (`SPDLOG_WARN`)
- ✅ 错误信息 (`SPDLOG_ERROR`)
- ✅ 内部状态跟踪

**特点**:
- 开发者和高级用户关心的信息
- 受 `--verbose`/`--quiet` 参数控制
- 可以重定向到日志文件
- 格式可以变化

## 实际应用示例

### ✅ 正确的 list 命令

```cpp
void list_installed_apps() {
    auto& config = Config::instance();
    auto apps_dir = config.get_apps_dir();
    
    // 调试信息 - 使用 spdlog
    SPDLOG_DEBUG("Checking apps directory: {}", apps_dir.string());
    
    if (!std::filesystem::exists(apps_dir)) {
        // 程序输出 - 使用 std::cout
        std::cout << "No apps installed.\n";
        return;
    }
    
    // 程序输出 - 使用 std::cout
    std::cout << "Installed apps:\n";
    
    for (const auto& entry : std::filesystem::directory_iterator(apps_dir)) {
        if (entry.is_directory()) {
            std::string app_name = entry.path().filename().string();
            std::string version = get_app_version(entry.path());
            
            // 程序输出 - 使用 std::cout
            std::cout << "  " << app_name << " (" << version << ")\n";
            
            // 调试信息 - 使用 spdlog
            SPDLOG_DEBUG("Found app: {} version {}", app_name, version);
        }
    }
}
```

### ✅ 正确的 config 命令

```cpp
void show_config_value(const std::string& key) {
    auto& config = Config::instance();
    
    // 调试信息 - 使用 spdlog
    SPDLOG_DEBUG("Getting config value for key: {}", key);
    
    std::string value = config.get(key);
    if (value.empty()) {
        // 错误输出 - 使用 std::cerr
        std::cerr << "Configuration key '" << key << "' not found.\n";
        // 日志记录 - 使用 spdlog
        SPDLOG_WARN("Configuration key '{}' not found", key);
        return;
    }
    
    // 程序输出 - 使用 std::cout
    std::cout << value << "\n";
}

void set_config_value(const std::string& key, const std::string& value) {
    auto& config = Config::instance();
    
    // 调试信息 - 使用 spdlog
    SPDLOG_DEBUG("Setting config: {} = {}", key, value);
    
    config.set(key, value);
    config.save();
    
    // 程序输出 - 使用 std::cout
    std::cout << "Set " << key << " to " << value << "\n";
    
    // 日志记录 - 使用 spdlog
    SPDLOG_INFO("Configuration updated: {} = {}", key, value);
}
```

### ✅ 正确的 help 命令

```cpp
int execute() override {
    if (command_name_.empty()) {
        // 帮助文档是程序输出 - 使用 std::cout
        std::cout << "Usage: sco <command> [<args>]\n\n";
        std::cout << "Available commands are listed below.\n\n";
        // ... 更多帮助信息
        
        // 调试信息 - 使用 spdlog
        SPDLOG_DEBUG("Help command executed successfully");
        return 0;
    }
    // ...
}
```

## 输出流选择

### std::cout
- ✅ 正常的程序输出
- ✅ 用户请求的数据
- ✅ 帮助信息

### std::cerr  
- ✅ 错误消息
- ✅ 警告信息
- ✅ 用户需要立即看到的重要信息

### spdlog 宏
- ✅ `SPDLOG_DEBUG` - 调试信息
- ✅ `SPDLOG_INFO` - 操作进度、状态信息
- ✅ `SPDLOG_WARN` - 警告（同时可能输出到 std::cerr）
- ✅ `SPDLOG_ERROR` - 错误（同时可能输出到 std::cerr）

## 用户体验考虑

### 脚本友好性
```bash
# 用户应该能够这样使用
sco list > installed_apps.txt
sco config aria2-enabled > current_setting.txt

# 而不会包含调试信息
```

### 日志级别控制
```bash
# 静默模式 - 只显示程序输出和错误
sco --quiet list

# 详细模式 - 显示程序输出 + 调试信息
sco --verbose install git

# 正常模式 - 显示程序输出 + 基本信息
sco install git
```

## 重构后的优势

1. **清晰的职责分离**
   - 程序输出专注于用户需要的数据
   - 日志信息专注于程序状态和调试

2. **更好的用户体验**
   - 脚本可以可靠地解析程序输出
   - 日志级别控制不影响核心功能

3. **符合 Unix 哲学**
   - 程序输出可以被管道处理
   - 错误信息通过 stderr 分离

4. **开发友好**
   - 调试信息丰富但不干扰用户
   - 日志可以重定向到文件

## 总结

正确的做法是：
- **程序输出** = `std::cout`/`std::cerr`
- **日志信息** = `spdlog` 宏

这样既保持了程序的可用性，又提供了丰富的调试和监控能力。
