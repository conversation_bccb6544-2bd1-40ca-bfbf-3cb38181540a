#include "command_manager.h"
#include "help_command.h"
#include "list_command.h"
#include <spdlog/spdlog.h>

namespace sco {

CommandManager::CommandManager() {
    // Commands will be registered when register_commands is called
}

void CommandManager::register_commands(CLI::App& app) {
    register_help_command(app);
    register_list_command(app);
    register_install_command(app);
    register_uninstall_command(app);
    register_update_command(app);
    register_search_command(app);
    register_info_command(app);
    register_home_command(app);
    register_bucket_command(app);
    register_config_command(app);
    register_cache_command(app);
    register_cat_command(app);
    register_checkup_command(app);
    register_cleanup_command(app);
    register_create_command(app);
    register_depends_command(app);
    register_download_command(app);
    register_export_command(app);
    register_import_command(app);
    register_hold_command(app);
    register_unhold_command(app);
    register_prefix_command(app);
    register_reset_command(app);
    register_shim_command(app);
    register_status_command(app);
    register_virustotal_command(app);
    register_which_command(app);
    register_alias_command(app);
}

int CommandManager::execute() {
    if (current_command_) {
        return current_command_();
    }
    return 0;
}

void CommandManager::register_command(const std::string& name, std::unique_ptr<BaseCommand> command) {
    commands_[name] = std::move(command);
}

void CommandManager::register_help_command(CLI::App& app) {
    auto help_cmd = std::make_unique<HelpCommand>();
    help_cmd->set_app(&app);
    
    auto* sub = app.add_subcommand("help", "Show help for a command");
    std::string command_name;
    sub->add_option("command", command_name, "Command to show help for");
    
    sub->callback([this, help_cmd_ptr = help_cmd.get(), &command_name]() {
        help_cmd_ptr->set_command_name(command_name);
        current_command_ = [help_cmd_ptr]() { return help_cmd_ptr->execute(); };
    });
    
    register_command("help", std::move(help_cmd));
}

void CommandManager::register_list_command(CLI::App& app) {
    auto list_cmd = std::make_unique<ListCommand>();
    
    auto* sub = app.add_subcommand("list", "List installed apps");
    
    sub->callback([this, list_cmd_ptr = list_cmd.get()]() {
        current_command_ = [list_cmd_ptr]() { return list_cmd_ptr->execute(); };
    });
    
    register_command("list", std::move(list_cmd));
}

// Placeholder implementations for other commands
void CommandManager::register_install_command(CLI::App& app) {
    auto* sub = app.add_subcommand("install", "Install apps");
    std::vector<std::string> apps;
    sub->add_option("apps", apps, "Apps to install")->required();
    
    sub->callback([this, apps]() {
        current_command_ = [apps]() {
            spdlog::info("Install command called with {} apps", apps.size());
            std::cout << "Install command not yet implemented.\n";
            std::cout << "Apps to install: ";
            for (const auto& app : apps) {
                std::cout << app << " ";
            }
            std::cout << "\n";
            return 0;
        };
    });
}

void CommandManager::register_uninstall_command(CLI::App& app) {
    auto* sub = app.add_subcommand("uninstall", "Uninstall an app");
    std::vector<std::string> apps;
    sub->add_option("apps", apps, "Apps to uninstall")->required();

    sub->callback([this, apps]() {
        current_command_ = [apps]() {
            spdlog::info("Uninstall command called with {} apps", apps.size());
            std::cout << "Uninstall command not yet implemented.\n";
            std::cout << "Apps to uninstall: ";
            for (const auto& app : apps) {
                std::cout << app << " ";
            }
            std::cout << "\n";
            return 0;
        };
    });
}

void CommandManager::register_update_command(CLI::App& app) {
    auto* sub = app.add_subcommand("update", "Update apps, or Scoop itself");
    std::vector<std::string> apps;
    bool all = false;
    sub->add_option("apps", apps, "Apps to update");
    sub->add_flag("--all,-a", all, "Update all apps");

    sub->callback([this, apps, all]() {
        current_command_ = [apps, all]() {
            if (all) {
                spdlog::info("Update all apps");
                std::cout << "Update all apps - not yet implemented.\n";
            } else if (!apps.empty()) {
                spdlog::info("Update specific apps");
                std::cout << "Update specific apps - not yet implemented.\n";
                std::cout << "Apps to update: ";
                for (const auto& app : apps) {
                    std::cout << app << " ";
                }
                std::cout << "\n";
            } else {
                spdlog::info("Update Scoop itself");
                std::cout << "Update Scoop itself - not yet implemented.\n";
            }
            return 0;
        };
    });
}

void CommandManager::register_search_command(CLI::App& app) {
    auto* sub = app.add_subcommand("search", "Search available apps");
    std::string query;
    sub->add_option("query", query, "Search query")->required();

    sub->callback([this, query]() {
        current_command_ = [query]() {
            spdlog::info("Search for: {}", query);
            std::cout << "Search command not yet implemented.\n";
            std::cout << "Search query: " << query << "\n";
            return 0;
        };
    });
}

void CommandManager::register_info_command(CLI::App& app) {
    auto* sub = app.add_subcommand("info", "Display information about an app");
    std::string app_name;
    sub->add_option("app", app_name, "App name")->required();

    sub->callback([this, app_name]() {
        current_command_ = [app_name]() {
            spdlog::info("Info for app: {}", app_name);
            std::cout << "Info command not yet implemented.\n";
            std::cout << "App: " << app_name << "\n";
            return 0;
        };
    });
}

void CommandManager::register_home_command(CLI::App& app) {
    auto* sub = app.add_subcommand("home", "Opens the app homepage");
    std::string app_name;
    sub->add_option("app", app_name, "App name")->required();

    sub->callback([this, app_name]() {
        current_command_ = [app_name]() {
            spdlog::info("Open homepage for app: {}", app_name);
            std::cout << "Home command not yet implemented.\n";
            std::cout << "App: " << app_name << "\n";
            return 0;
        };
    });
}

// Placeholder implementations for remaining commands
void CommandManager::register_bucket_command(CLI::App& app) {
    auto* sub = app.add_subcommand("bucket", "Manage Scoop buckets");
    sub->callback([this]() {
        current_command_ = []() {
            std::cout << "Bucket command not yet implemented.\n";
            return 0;
        };
    });
}

void CommandManager::register_config_command(CLI::App& app) {
    auto* sub = app.add_subcommand("config", "Get or set configuration values");
    sub->callback([this]() {
        current_command_ = []() {
            std::cout << "Config command not yet implemented.\n";
            return 0;
        };
    });
}

void CommandManager::register_cache_command(CLI::App& app) {
    auto* sub = app.add_subcommand("cache", "Show or clear the download cache");
    sub->callback([this]() {
        current_command_ = []() {
            std::cout << "Cache command not yet implemented.\n";
            return 0;
        };
    });
}

void CommandManager::register_cat_command(CLI::App& app) {
    auto* sub = app.add_subcommand("cat", "Show content of specified manifest");
    std::string app_name;
    sub->add_option("app", app_name, "App name")->required();

    sub->callback([this, app_name]() {
        current_command_ = [app_name]() {
            std::cout << "Cat command not yet implemented.\n";
            std::cout << "App: " << app_name << "\n";
            return 0;
        };
    });
}

void CommandManager::register_checkup_command(CLI::App& app) {
    auto* sub = app.add_subcommand("checkup", "Check for potential problems");
    sub->callback([this]() {
        current_command_ = []() {
            std::cout << "Checkup command not yet implemented.\n";
            return 0;
        };
    });
}

void CommandManager::register_cleanup_command(CLI::App& app) {
    auto* sub = app.add_subcommand("cleanup", "Cleanup apps by removing old versions");
    sub->callback([this]() {
        current_command_ = []() {
            std::cout << "Cleanup command not yet implemented.\n";
            return 0;
        };
    });
}

void CommandManager::register_create_command(CLI::App& app) {
    auto* sub = app.add_subcommand("create", "Create a custom app manifest");
    std::string app_name;
    sub->add_option("app", app_name, "App name")->required();

    sub->callback([this, app_name]() {
        current_command_ = [app_name]() {
            std::cout << "Create command not yet implemented.\n";
            std::cout << "App: " << app_name << "\n";
            return 0;
        };
    });
}

void CommandManager::register_depends_command(CLI::App& app) {
    auto* sub = app.add_subcommand("depends", "List dependencies for an app");
    std::string app_name;
    sub->add_option("app", app_name, "App name")->required();

    sub->callback([this, app_name]() {
        current_command_ = [app_name]() {
            std::cout << "Depends command not yet implemented.\n";
            std::cout << "App: " << app_name << "\n";
            return 0;
        };
    });
}
