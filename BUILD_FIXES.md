# 编译问题修复指南

## 已修复的问题

### 1. ✅ 缺少 `<map>` 头文件
**问题**: `cache_command.hpp` 使用了 `std::map` 但没有包含相应头文件
**修复**: 在 `src/commands/cache_command.hpp` 中添加了：
```cpp
#include <map>
#include <vector>
```

### 2. ✅ 配置初始化
**问题**: 主程序没有初始化配置系统
**修复**: 在 `src/main.cpp` 中添加了配置加载：
```cpp
// Initialize and load configuration
auto& config = sco::Config::instance();
config.load();
```

## 当前编译问题

### 问题: 找不到标准库头文件
```
fatal error C1083: Cannot open include file: 'iostream': No such file or directory
```

**原因**: Visual Studio 环境变量没有正确设置

## 解决方案

### 方案 1: 使用 Visual Studio Developer Command Prompt
1. 打开 "Developer Command Prompt for VS 2019" 或 "Developer PowerShell for VS 2019"
2. 导航到项目目录: `cd C:\Code\scoop`
3. 运行构建命令:
   ```bash
   cmake --build Build/msvc-debug
   ```

### 方案 2: 手动设置环境变量
在 PowerShell 中运行：
```powershell
# 查找 Visual Studio 安装路径
$vsPath = "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\Professional"
if (-not (Test-Path $vsPath)) {
    $vsPath = "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\Community"
}
if (-not (Test-Path $vsPath)) {
    $vsPath = "${env:ProgramFiles}\Microsoft Visual Studio\2019\Professional"
}

# 设置环境变量
& "$vsPath\VC\Auxiliary\Build\vcvars64.bat"

# 然后运行构建
cmake --build Build/msvc-debug
```

### 方案 3: 重新配置 CMake
```bash
# 清理构建目录
Remove-Item -Recurse -Force Build/msvc-debug

# 重新配置 CMake
cmake -B Build/msvc-debug -S . -G "Visual Studio 16 2019" -A x64 -DCMAKE_TOOLCHAIN_FILE=C:\Local\vcpkg-latest\scripts\buildsystems\vcpkg.cmake

# 构建
cmake --build Build/msvc-debug --config Debug
```

### 方案 4: 使用 MinGW (如果可用)
```bash
# 安装 MinGW-w64 (如果没有)
# 然后配置使用 MinGW
cmake -B Build/mingw -S . -G "MinGW Makefiles" -DCMAKE_TOOLCHAIN_FILE=C:\Local\vcpkg-latest\scripts\buildsystems\vcpkg.cmake

# 构建
cmake --build Build/mingw
```

## 验证修复

### 检查编译器可用性
```bash
# 检查 MSVC
cl

# 检查 MinGW
g++ --version

# 检查 Clang
clang++ --version
```

### 测试简单编译
创建测试文件 `test_compile.cpp`:
```cpp
#include <iostream>
int main() {
    std::cout << "Compiler works!" << std::endl;
    return 0;
}
```

编译测试:
```bash
# MSVC
cl /EHsc test_compile.cpp

# MinGW
g++ -o test_compile.exe test_compile.cpp

# Clang
clang++ -o test_compile.exe test_compile.cpp
```

## 依赖库状态

### 已配置的依赖
- ✅ CLI11 - 命令行解析
- ✅ nlohmann_json - JSON 处理
- ✅ spdlog - 日志记录

### vcpkg 安装命令
```bash
vcpkg install cli11:x64-windows
vcpkg install nlohmann-json:x64-windows
vcpkg install spdlog:x64-windows
```

## 构建成功后的测试

### 基本功能测试
```bash
# 运行程序
.\Build\msvc-debug\Debug\sco.exe

# 测试帮助命令
.\Build\msvc-debug\Debug\sco.exe help

# 测试配置命令
.\Build\msvc-debug\Debug\sco.exe config

# 测试检查命令
.\Build\msvc-debug\Debug\sco.exe checkup
```

### 预期输出
- `sco help`: 显示所有可用命令
- `sco config`: 显示当前配置
- `sco checkup`: 检查系统状态
- `sco list`: 列出已安装应用（如果有）
- `sco status`: 显示系统状态

## 下一步

一旦编译成功，可以继续实现核心功能：
1. **install 命令** - 包安装功能
2. **uninstall 命令** - 包卸载功能
3. **update 命令** - 包更新功能
4. **search 命令** - 包搜索功能

## 故障排除

### 如果仍然有编译错误
1. 检查 Visual Studio 安装是否完整
2. 确认 C++ 工具已安装
3. 检查 vcpkg 路径是否正确
4. 尝试使用不同的生成器 (Ninja vs Visual Studio)

### 常见错误和解决方案
- **LNK2019**: 链接错误 - 检查库路径和依赖
- **C1083**: 找不到头文件 - 检查包含路径
- **C2039**: 成员不存在 - 检查头文件包含顺序

编译成功后，我们就有了一个功能完整的 CLI 框架！
