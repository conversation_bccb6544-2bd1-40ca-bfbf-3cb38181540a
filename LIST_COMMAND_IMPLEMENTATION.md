# List 命令表格格式实现总结

## ✅ 已完成的改进

我已经成功重写了 `list_command.hpp`，实现了与原版 Scoop 相同的表格格式输出。

### 🎯 新的输出格式

**之前的简单格式**:
```
Installed apps:
  7zip (24.09)
  vscode (1.101.1)
  ...
```

**现在的表格格式**:
```
Installed apps:

Name				Version			Source	Updated			Info
----				-------			------	-------			----
7zip				24.09			main	2025-06-20 20:26:47
everything			1.4.1.1027		extras	2025-06-20 20:50:55
vscode				1.101.1			extras	2025-06-21 22:18:20
...
```

## 🔧 技术实现

### 1. 数据结构
```cpp
struct AppInfo {
    std::string name;      // 应用名称
    std::string version;   // 版本号
    std::string source;    // 来源 bucket
    std::string updated;   // 更新时间
    std::string info;      // 附加信息
};
```

### 2. 信息获取方法

#### 版本信息 (`get_app_version`)
- 优先读取 `current.txt` 文件
- 备用方案：扫描版本目录名
- 跳过特殊目录 (`current`, `persist`)

#### 来源信息 (`get_app_source`)
- 读取 `install.json` 中的 `bucket` 字段
- 备用文件：`scoop-install.json`
- JSON 解析错误时显示 "unknown"

#### 更新时间 (`get_app_updated_time`)
- 获取 `current` 目录的最后修改时间
- 备用：`current.txt` 文件的修改时间
- 格式：`YYYY-MM-DD HH:MM:SS`

#### 附加信息 (`get_app_info`)
- 预留扩展功能
- 未来可显示：global, held, failed, outdated 等状态

### 3. 表格打印 (`print_apps_table`)
- 使用制表符 (`\t`) 分隔列
- 动态计算列宽度
- 自动对齐表格内容
- 按应用名称排序

## 📁 支持的目录结构

```
{root_path}/apps/
├── 7zip/
│   ├── current.txt          # 版本信息: "24.09"
│   ├── current/             # 当前版本符号链接
│   │   ├── install.json     # {"bucket": "main", ...}
│   │   └── ...
│   └── 24.09/              # 实际版本目录
├── vscode/
│   ├── current.txt          # 版本信息: "1.101.1"
│   ├── current/
│   │   ├── scoop-install.json  # 备用安装信息文件
│   │   └── ...
│   └── 1.101.1/
└── ...
```

## 🛡️ 错误处理

### 文件系统错误
- 目录不存在：显示 "No apps installed"
- 权限问题：跳过该应用，继续处理
- 文件损坏：使用备用方法获取信息

### 数据解析错误
- JSON 格式错误：显示 "unknown" 来源
- 时间获取失败：显示空字符串
- 版本信息缺失：显示 "unknown"

### 异常安全
- 所有文件操作都有异常处理
- 单个应用错误不影响整体列表
- 详细的调试日志记录

## 🔍 调试支持

### 详细日志
```cpp
SPDLOG_DEBUG("Checking apps directory: {}", apps_dir.string());
SPDLOG_DEBUG("Found app: {} version {} from {}", app_name, version, source);
SPDLOG_ERROR("Filesystem error: {}", e.what());
```

### 命令行调试
```bash
sco --verbose list
# 显示:
# - 扫描的目录路径
# - 每个应用的详细信息
# - 文件读取过程
# - 错误和警告
```

## 🎨 格式化特性

### 智能列宽
- Name 列：目标宽度 25 字符
- Version 列：目标宽度 16 字符
- 自动计算所需制表符数量
- 保证表格整齐对齐

### 排序
- 按应用名称字母顺序排列
- 忽略大小写差异
- 稳定排序算法

### 表头
- 清晰的列标题
- 分隔线对齐
- 易于阅读的格式

## 🔄 兼容性

### 与原版 Scoop 兼容
- ✅ 读取相同的文件结构
- ✅ 解析相同的元数据格式
- ✅ 显示相同的信息内容
- ✅ 类似的表格布局

### 数据源兼容
- ✅ `current.txt` 版本文件
- ✅ `install.json` 安装信息
- ✅ `scoop-install.json` 备用格式
- ✅ 版本目录结构

## 🚀 性能优化

### 高效扫描
- 单次目录遍历
- 按需读取文件
- 最小化文件系统操作

### 内存管理
- 使用 `std::vector` 存储应用信息
- 及时关闭文件句柄
- 避免不必要的字符串复制

### 错误恢复
- 单个应用错误不影响整体
- 优雅降级到备用数据源
- 快速跳过损坏的应用

## 📈 未来扩展

### Info 列扩展
```cpp
// 未来可能的状态显示
"global"    // 全局安装
"held"      // 版本锁定
"failed"    // 安装失败
"outdated"  // 有可用更新
```

### 过滤选项
```bash
# 未来可能的功能
sco list --source main        # 按来源过滤
sco list --outdated          # 只显示过期应用
sco list --global            # 只显示全局应用
```

### 输出格式
```bash
# 未来可能的格式选项
sco list --format json       # JSON 输出
sco list --format csv        # CSV 输出
sco list --no-header         # 无表头
```

## 📊 代码质量

### 现代 C++ 特性
- ✅ 使用 `std::filesystem` 进行路径操作
- ✅ RAII 资源管理
- ✅ 异常安全的设计
- ✅ 类型安全的 JSON 解析

### 可维护性
- ✅ 清晰的函数职责分离
- ✅ 详细的错误处理
- ✅ 丰富的调试信息
- ✅ 易于扩展的架构

## 🎉 总结

新的 `list` 命令实现提供了：

1. **完整的表格格式** - 与原版 Scoop 一致的输出
2. **丰富的信息显示** - 名称、版本、来源、更新时间
3. **强大的错误处理** - 优雅处理各种异常情况
4. **高性能扫描** - 高效的文件系统操作
5. **完全兼容性** - 支持现有 Scoop 安装

这为用户提供了更好的应用管理体验，同时为后续功能开发奠定了坚实基础！
