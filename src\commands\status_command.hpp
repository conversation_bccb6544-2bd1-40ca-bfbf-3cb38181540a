#pragma once

#include "base_command.hpp"
#include "../core/config.hpp"
#include "../utils/table_formatter.hpp"
#include <iostream>
#include <filesystem>
#include <fstream>
#include <spdlog/spdlog.h>

namespace sco {

class StatusCommand : public BaseCommand {
public:
    StatusCommand() = default;
    
    int execute() override {
        try {
            show_scoop_status();
            show_installed_apps_status();
            show_buckets_status();
            show_cache_status();
            return 0;
        } catch (const std::exception& e) {
            SPDLOG_ERROR("Status command failed: {}", e.what());
            return 1;
        }
    }
    
    std::string get_name() const override { return "status"; }
    std::string get_description() const override { return "Show status and check for new app versions"; }
    
private:
    void show_scoop_status() {
        SPDLOG_INFO("Scoop Status");
        SPDLOG_INFO("============");

        auto& config = Config::instance();
        config.load();

        auto scoop_dir = config.get_scoop_dir();

        if (std::filesystem::exists(scoop_dir)) {
            SPDLOG_INFO("Scoop directory: {}", scoop_dir.string());
            SPDLOG_INFO("Global mode: {}", config.is_global_mode() ? "Yes" : "No");

            // Show disk usage
            try {
                auto space_info = std::filesystem::space(scoop_dir);
                auto used_space = space_info.capacity - space_info.available;
                auto scoop_size = get_directory_size(scoop_dir);

                SPDLOG_INFO("Scoop size: {}", format_bytes(scoop_size));
                SPDLOG_INFO("Available space: {}", format_bytes(space_info.available));
            } catch (const std::exception& e) {
                SPDLOG_DEBUG("Could not get disk space info: {}", e.what());
            }
        } else {
            SPDLOG_INFO("Scoop not initialized. Run 'sco install <app>' to initialize.");
        }

        SPDLOG_INFO("");
    }
    
    void show_installed_apps_status() {
        std::cout << "Installed Apps\n";
        std::cout << "==============\n\n";

        auto& config = Config::instance();
        auto apps_dir = config.get_apps_dir();

        if (!std::filesystem::exists(apps_dir)) {
            std::cout << "No apps installed.\n\n";
            return;
        }

        TableFormatter table;
        table.add_column("Name", 20, true);
        table.add_column("Version", 15, true);
        table.add_column("Status", 12, true);

        int app_count = 0;
        int outdated_count = 0;

        try {
            for (const auto& entry : std::filesystem::directory_iterator(apps_dir)) {
                if (entry.is_directory()) {
                    std::string app_name = entry.path().filename().string();

                    // Skip scoop itself
                    if (app_name == "scoop") {
                        continue;
                    }

                    app_count++;

                    // Find current version
                    auto app_path = entry.path();
                    std::string current_version = get_app_version(app_path);

                    // Check if app might be outdated (placeholder logic)
                    std::string status = "Up to date";
                    if (is_app_potentially_outdated(app_name, current_version)) {
                        status = "Update available";
                        outdated_count++;
                    }

                    table.add_row({app_name, current_version, status});
                }
            }
        } catch (const std::filesystem::filesystem_error& e) {
            SPDLOG_ERROR("Filesystem error: {}", e.what());
        }

        if (app_count > 0) {
            table.auto_adjust_widths();
            table.print();
            std::cout << "\n";
        }

        std::cout << "Total apps: " << app_count << "\n";
        if (outdated_count > 0) {
            std::cout << "Apps with updates available: " << outdated_count << "\n";
            std::cout << "Run 'sco update --all' to update all apps.\n";
        }
        std::cout << "\n";
    }
    
    void show_buckets_status() {
        SPDLOG_INFO("Buckets");
        SPDLOG_INFO("=======");

        auto& config = Config::instance();
        auto buckets_dir = config.get_buckets_dir();

        if (!std::filesystem::exists(buckets_dir)) {
            SPDLOG_INFO("No buckets added.");
            SPDLOG_INFO("Add the main bucket: sco bucket add main");
            SPDLOG_INFO("");
            return;
        }

        int bucket_count = 0;

        try {
            for (const auto& entry : std::filesystem::directory_iterator(buckets_dir)) {
                if (entry.is_directory()) {
                    std::string bucket_name = entry.path().filename().string();
                    bucket_count++;

                    // Count manifests in bucket
                    int manifest_count = 0;
                    auto bucket_path = entry.path();

                    if (std::filesystem::exists(bucket_path / "bucket")) {
                        for (const auto& manifest : std::filesystem::directory_iterator(bucket_path / "bucket")) {
                            if (manifest.path().extension() == ".json") {
                                manifest_count++;
                            }
                        }
                    }

                    SPDLOG_INFO("  {} ({} apps)", bucket_name, manifest_count);
                }
            }
        } catch (const std::filesystem::filesystem_error& e) {
            SPDLOG_ERROR("Filesystem error: {}", e.what());
        }

        SPDLOG_INFO("Total buckets: {}", bucket_count);
        SPDLOG_INFO("");
    }
    
    void show_cache_status() {
        SPDLOG_INFO("Cache");
        SPDLOG_INFO("=====");

        auto& config = Config::instance();
        auto cache_dir = config.get_cache_dir();

        if (!std::filesystem::exists(cache_dir)) {
            SPDLOG_INFO("Cache directory does not exist.");
            SPDLOG_INFO("");
            return;
        }

        try {
            auto cache_size = get_directory_size(cache_dir);
            int file_count = 0;

            for (const auto& entry : std::filesystem::recursive_directory_iterator(cache_dir)) {
                if (entry.is_regular_file()) {
                    file_count++;
                }
            }

            SPDLOG_INFO("Cache size: {}", format_bytes(cache_size));
            SPDLOG_INFO("Cached files: {}", file_count);

            if (cache_size > 100 * 1024 * 1024) { // > 100MB
                SPDLOG_INFO("Consider cleaning cache: sco cache rm *");
            }
        } catch (const std::filesystem::filesystem_error& e) {
            SPDLOG_ERROR("Filesystem error: {}", e.what());
        }

        SPDLOG_INFO("");
    }
    
    std::string get_app_version(const std::filesystem::path& app_path) {
        // Look for current.txt or check for version directories
        auto current_file = app_path / "current.txt";
        if (std::filesystem::exists(current_file)) {
            std::ifstream file(current_file);
            if (file.is_open()) {
                std::string version;
                std::getline(file, version);
                return version;
            }
        }
        
        // Check for version directories
        for (const auto& version_entry : std::filesystem::directory_iterator(app_path)) {
            if (version_entry.is_directory()) {
                return version_entry.path().filename().string();
            }
        }
        
        return "unknown";
    }
    
    bool is_app_potentially_outdated(const std::string& app_name, const std::string& version) {
        // Placeholder logic - in real implementation, this would check against
        // bucket manifests or online sources
        (void)app_name; // Suppress unused parameter warning
        (void)version;
        return false; // For now, assume no updates available
    }
    
    std::uintmax_t get_directory_size(const std::filesystem::path& path) {
        std::uintmax_t size = 0;
        try {
            for (const auto& entry : std::filesystem::recursive_directory_iterator(path)) {
                if (entry.is_regular_file()) {
                    size += entry.file_size();
                }
            }
        } catch (const std::filesystem::filesystem_error&) {
            // Ignore errors and return partial size
        }
        return size;
    }
    
    std::string format_bytes(std::uintmax_t bytes) {
        const char* units[] = {"B", "KB", "MB", "GB", "TB"};
        int unit = 0;
        double size = static_cast<double>(bytes);
        
        while (size >= 1024.0 && unit < 4) {
            size /= 1024.0;
            unit++;
        }
        
        char buffer[32];
        if (unit == 0) {
            snprintf(buffer, sizeof(buffer), "%.0f %s", size, units[unit]);
        } else {
            snprintf(buffer, sizeof(buffer), "%.1f %s", size, units[unit]);
        }
        
        return std::string(buffer);
    }
};

} // namespace sco
