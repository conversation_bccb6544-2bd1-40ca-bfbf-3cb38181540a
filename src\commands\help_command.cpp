#include "help_command.h"
#include <iostream>
#include <spdlog/spdlog.h>

namespace sco {

int HelpCommand::execute() {
    if (command_name_.empty()) {
        // Show general help
        std::cout << "Usage: sco <command> [<args>]\n\n";
        std::cout << "Available commands are listed below.\n\n";
        std::cout << "Type 'sco help <command>' to get more help for a specific command.\n\n";
        
        std::cout << "Command    Summary\n";
        std::cout << "-------    -------\n";
        std::cout << "alias      Manage scoop aliases\n";
        std::cout << "bucket     Manage Scoop buckets\n";
        std::cout << "cache      Show or clear the download cache\n";
        std::cout << "cat        Show content of specified manifest\n";
        std::cout << "checkup    Check for potential problems\n";
        std::cout << "cleanup    Cleanup apps by removing old versions\n";
        std::cout << "config     Get or set configuration values\n";
        std::cout << "create     Create a custom app manifest\n";
        std::cout << "depends    List dependencies for an app, in the order they'll be installed\n";
        std::cout << "download   Download apps in the cache folder and verify hashes\n";
        std::cout << "export     Exports installed apps, buckets (and optionally configs) in JSON format\n";
        std::cout << "help       Show help for a command\n";
        std::cout << "hold       Hold an app to disable updates\n";
        std::cout << "home       Opens the app homepage\n";
        std::cout << "import     Imports apps, buckets and configs from a Scoopfile in JSON format\n";
        std::cout << "info       Display information about an app\n";
        std::cout << "install    Install apps\n";
        std::cout << "list       List installed apps\n";
        std::cout << "prefix     Returns the path to the specified app\n";
        std::cout << "reset      Reset an app to resolve conflicts\n";
        std::cout << "search     Search available apps\n";
        std::cout << "shim       Manipulate Scoop shims\n";
        std::cout << "status     Show status and check for new app versions\n";
        std::cout << "unhold     Unhold an app to enable updates\n";
        std::cout << "uninstall  Uninstall an app\n";
        std::cout << "update     Update apps, or Scoop itself\n";
        std::cout << "virustotal Look for app's hash or url on virustotal.com\n";
        std::cout << "which      Locate a shim/executable (similar to 'which' on Linux)\n";
        
        return 0;
    } else {
        // Show help for specific command
        if (app_) {
            try {
                auto* subcommand = app_->get_subcommand(command_name_);
                if (subcommand) {
                    std::cout << subcommand->help();
                    return 0;
                }
            } catch (const std::exception&) {
                // Command not found
            }
        }
        
        std::cout << "Unknown command: " << command_name_ << "\n";
        std::cout << "Type 'sco help' to see available commands.\n";
        return 1;
    }
}
