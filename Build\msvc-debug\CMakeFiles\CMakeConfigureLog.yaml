
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:4 (project)"
    message: |
      The system is: Windows - 10.0.17763 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: cl 
      Build flags: 
      Id flags:  
      
      The output was:
      no such file or directory
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: cl 
      Build flags: 
      Id flags: -c 
      
      The output was:
      no such file or directory
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: cl 
      Build flags: 
      Id flags: --c++ 
      
      The output was:
      no such file or directory
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: cl 
      Build flags: 
      Id flags: --ec++ 
      
      The output was:
      no such file or directory
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: cl 
      Build flags: 
      Id flags: --target=arm-arm-none-eabi;-mcpu=cortex-m3 
      
      The output was:
      no such file or directory
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: cl 
      Build flags: 
      Id flags: -c;-I__does_not_exist__ 
      
      The output was:
      no such file or directory
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: cl 
      Build flags: 
      Id flags:  
      
      The output was:
      no such file or directory
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: cl 
      Build flags: 
      Id flags: -c 
      
      The output was:
      no such file or directory
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: cl 
      Build flags: 
      Id flags: --c++ 
      
      The output was:
      no such file or directory
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: cl 
      Build flags: 
      Id flags: --ec++ 
      
      The output was:
      no such file or directory
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: cl 
      Build flags: 
      Id flags: --target=arm-arm-none-eabi;-mcpu=cortex-m3 
      
      The output was:
      no such file or directory
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: cl 
      Build flags: 
      Id flags: -c;-I__does_not_exist__ 
      
      The output was:
      no such file or directory
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:1205 (message)"
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:86 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Checking whether the CXX compiler is IAR using "" did not match "IAR .+ Compiler":
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:1205 (message)"
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:86 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Checking whether the CXX compiler is IAR using "" did not match "IAR .+ Compiler":
...
