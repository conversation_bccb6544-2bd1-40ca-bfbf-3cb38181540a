
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.17763 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: C:/Program Files (x86)/Microsoft Visual Studio/2019/Professional/VC/Tools/MSVC/14.29.30133/bin/Hostx64/x64/cl.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      Microsoft (R) C/C++ Optimizing Compiler Version 19.29.30159 for x64
      Copyright (C) Microsoft Corporation.  All rights reserved.
      
      CMakeCXXCompilerId.cpp
      Microsoft (R) Incremental Linker Version 14.29.30159.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
      
      /out:CMakeCXXCompilerId.exe 
      CMakeCXXCompilerId.obj 
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.obj"
      
      The CXX compiler identification is MSVC, found in:
        C:/Code/scoop/Build/msvc-debug/CMakeFiles/3.30.1/CompilerIdCXX/CMakeCXXCompilerId.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:1243 (message)"
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:250 (CMAKE_DETERMINE_MSVC_SHOWINCLUDES_PREFIX)"
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Detecting CXX compiler /showIncludes prefix:
        main.c
        Note: including file: C:\\Code\\scoop\\Build\\msvc-debug\\CMakeFiles\\ShowIncludes\\foo.h
        
      Found prefix "Note: including file: "
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "C:/Code/scoop/Build/msvc-debug/CMakeFiles/CMakeScratch/TryCompile-tmw0f8"
      binary: "C:/Code/scoop/Build/msvc-debug/CMakeFiles/CMakeScratch/TryCompile-tmw0f8"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /W3 /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "C:/Local/vcpkg-latest/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "C:/Local/vcpkg-latest"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Code/scoop/Build/msvc-debug/CMakeFiles/CMakeScratch/TryCompile-tmw0f8'
        
        Run Build Command(s): C:/Users/<USER>/OneDrive/link/ninja.exe -v cmTC_9a8a4
        [1/2] C:\\PROGRA~2\\MICROS~2\\2019\\PROFES~1\\VC\\Tools\\MSVC\\1429~1.301\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP   /DWIN32 /D_WINDOWS /W3 /GR /EHsc  /MDd /Zi /Ob0 /Od /RTC1 /showIncludes /FoCMakeFiles\\cmTC_9a8a4.dir\\CMakeCXXCompilerABI.cpp.obj /FdCMakeFiles\\cmTC_9a8a4.dir\\ /FS -c C:\\Users\\<USER>\\OneDrive\\app\\cmake\\share\\cmake-3.30\\Modules\\CMakeCXXCompilerABI.cpp
        [2/2] C:\\WINDOWS\\system32\\cmd.exe /C "cd . && C:\\Users\\<USER>\\OneDrive\\app\\cmake\\bin\\cmake.exe -E vs_link_exe --intdir=CMakeFiles\\cmTC_9a8a4.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100190~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100190~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~2\\MICROS~2\\2019\\PROFES~1\\VC\\Tools\\MSVC\\1429~1.301\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_9a8a4.dir\\CMakeCXXCompilerABI.cpp.obj  /out:cmTC_9a8a4.exe /implib:cmTC_9a8a4.lib /pdb:cmTC_9a8a4.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/PROGRA~2/MICROS~2/2019/PROFES~1/VC/Tools/MSVC/1429~1.301/bin/Hostx64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "C:/PROGRA~2/MICROS~2/2019/PROFES~1/VC/Tools/MSVC/1429~1.301/bin/Hostx64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.29.30159.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/Internal/CheckSourceCompiles.cmake:101 (try_compile)"
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CheckCXXSourceCompiles.cmake:52 (cmake_check_source_compiles)"
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/FindThreads.cmake:99 (CHECK_CXX_SOURCE_COMPILES)"
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "C:/Local/vcpkg-latest/scripts/buildsystems/vcpkg.cmake:893 (_find_package)"
      - "C:/Local/vcpkg-latest/installed/x64-windows/share/spdlog/spdlogConfig.cmake:30 (find_package)"
      - "C:/Local/vcpkg-latest/scripts/buildsystems/vcpkg.cmake:893 (_find_package)"
      - "CMakeLists.txt:15 (find_package)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "C:/Code/scoop/Build/msvc-debug/CMakeFiles/CMakeScratch/TryCompile-av7xvu"
      binary: "C:/Code/scoop/Build/msvc-debug/CMakeFiles/CMakeScratch/TryCompile-av7xvu"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /W3 /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "C:/Local/vcpkg-latest/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "C:/Local/vcpkg-latest"
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: 'C:/Code/scoop/Build/msvc-debug/CMakeFiles/CMakeScratch/TryCompile-av7xvu'
        
        Run Build Command(s): C:/Users/<USER>/OneDrive/link/ninja.exe -v cmTC_cea5e
        [1/2] C:\\PROGRA~2\\MICROS~2\\2019\\PROFES~1\\VC\\Tools\\MSVC\\1429~1.301\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DCMAKE_HAVE_LIBC_PTHREAD  /DWIN32 /D_WINDOWS /W3 /GR /EHsc  /MDd /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /showIncludes /FoCMakeFiles\\cmTC_cea5e.dir\\src.cxx.obj /FdCMakeFiles\\cmTC_cea5e.dir\\ /FS -c C:\\Code\\scoop\\Build\\msvc-debug\\CMakeFiles\\CMakeScratch\\TryCompile-av7xvu\\src.cxx
        FAILED: CMakeFiles/cmTC_cea5e.dir/src.cxx.obj 
        C:\\PROGRA~2\\MICROS~2\\2019\\PROFES~1\\VC\\Tools\\MSVC\\1429~1.301\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DCMAKE_HAVE_LIBC_PTHREAD  /DWIN32 /D_WINDOWS /W3 /GR /EHsc  /MDd /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /showIncludes /FoCMakeFiles\\cmTC_cea5e.dir\\src.cxx.obj /FdCMakeFiles\\cmTC_cea5e.dir\\ /FS -c C:\\Code\\scoop\\Build\\msvc-debug\\CMakeFiles\\CMakeScratch\\TryCompile-av7xvu\\src.cxx
        C:\\Code\\scoop\\Build\\msvc-debug\\CMakeFiles\\CMakeScratch\\TryCompile-av7xvu\\src.cxx(1): fatal error C1083: Cannot open include file: 'pthread.h': No such file or directory
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CheckLibraryExists.cmake:69 (try_compile)"
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/FindThreads.cmake:112 (CHECK_LIBRARY_EXISTS)"
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/FindThreads.cmake:175 (_threads_check_lib)"
      - "C:/Local/vcpkg-latest/scripts/buildsystems/vcpkg.cmake:893 (_find_package)"
      - "C:/Local/vcpkg-latest/installed/x64-windows/share/spdlog/spdlogConfig.cmake:30 (find_package)"
      - "C:/Local/vcpkg-latest/scripts/buildsystems/vcpkg.cmake:893 (_find_package)"
      - "CMakeLists.txt:15 (find_package)"
    checks:
      - "Looking for pthread_create in pthreads"
    directories:
      source: "C:/Code/scoop/Build/msvc-debug/CMakeFiles/CMakeScratch/TryCompile-gsxjgs"
      binary: "C:/Code/scoop/Build/msvc-debug/CMakeFiles/CMakeScratch/TryCompile-gsxjgs"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /W3 /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "C:/Local/vcpkg-latest/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "C:/Local/vcpkg-latest"
    buildResult:
      variable: "CMAKE_HAVE_PTHREADS_CREATE"
      cached: true
      stdout: |
        Change Dir: 'C:/Code/scoop/Build/msvc-debug/CMakeFiles/CMakeScratch/TryCompile-gsxjgs'
        
        Run Build Command(s): C:/Users/<USER>/OneDrive/link/ninja.exe -v cmTC_ef4e3
        [1/2] C:\\PROGRA~2\\MICROS~2\\2019\\PROFES~1\\VC\\Tools\\MSVC\\1429~1.301\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP   /DWIN32 /D_WINDOWS /W3 /GR /EHsc -DCHECK_FUNCTION_EXISTS=pthread_create /MDd /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /showIncludes /FoCMakeFiles\\cmTC_ef4e3.dir\\CheckFunctionExists.cxx.obj /FdCMakeFiles\\cmTC_ef4e3.dir\\ /FS -c C:\\Code\\scoop\\Build\\msvc-debug\\CMakeFiles\\CMakeScratch\\TryCompile-gsxjgs\\CheckFunctionExists.cxx
        [2/2] C:\\WINDOWS\\system32\\cmd.exe /C "cd . && C:\\Users\\<USER>\\OneDrive\\app\\cmake\\bin\\cmake.exe -E vs_link_exe --intdir=CMakeFiles\\cmTC_ef4e3.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100190~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100190~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~2\\MICROS~2\\2019\\PROFES~1\\VC\\Tools\\MSVC\\1429~1.301\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_ef4e3.dir\\CheckFunctionExists.cxx.obj  /out:cmTC_ef4e3.exe /implib:cmTC_ef4e3.lib /pdb:cmTC_ef4e3.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  pthreads.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        FAILED: cmTC_ef4e3.exe 
        C:\\WINDOWS\\system32\\cmd.exe /C "cd . && C:\\Users\\<USER>\\OneDrive\\app\\cmake\\bin\\cmake.exe -E vs_link_exe --intdir=CMakeFiles\\cmTC_ef4e3.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100190~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100190~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~2\\MICROS~2\\2019\\PROFES~1\\VC\\Tools\\MSVC\\1429~1.301\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_ef4e3.dir\\CheckFunctionExists.cxx.obj  /out:cmTC_ef4e3.exe /implib:cmTC_ef4e3.lib /pdb:cmTC_ef4e3.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  pthreads.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        LINK Pass 1: command "C:\\PROGRA~2\\MICROS~2\\2019\\PROFES~1\\VC\\Tools\\MSVC\\1429~1.301\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_ef4e3.dir\\CheckFunctionExists.cxx.obj /out:cmTC_ef4e3.exe /implib:cmTC_ef4e3.lib /pdb:cmTC_ef4e3.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console pthreads.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTFILE:CMakeFiles\\cmTC_ef4e3.dir/intermediate.manifest CMakeFiles\\cmTC_ef4e3.dir/manifest.res" failed (exit code 1104) with the following output:
        LINK : fatal error LNK1104: cannot open file 'pthreads.lib'\x0d
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/CheckLibraryExists.cmake:69 (try_compile)"
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/FindThreads.cmake:112 (CHECK_LIBRARY_EXISTS)"
      - "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30/Modules/FindThreads.cmake:176 (_threads_check_lib)"
      - "C:/Local/vcpkg-latest/scripts/buildsystems/vcpkg.cmake:893 (_find_package)"
      - "C:/Local/vcpkg-latest/installed/x64-windows/share/spdlog/spdlogConfig.cmake:30 (find_package)"
      - "C:/Local/vcpkg-latest/scripts/buildsystems/vcpkg.cmake:893 (_find_package)"
      - "CMakeLists.txt:15 (find_package)"
    checks:
      - "Looking for pthread_create in pthread"
    directories:
      source: "C:/Code/scoop/Build/msvc-debug/CMakeFiles/CMakeScratch/TryCompile-z6njw8"
      binary: "C:/Code/scoop/Build/msvc-debug/CMakeFiles/CMakeScratch/TryCompile-z6njw8"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /W3 /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "C:/Local/vcpkg-latest/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "C:/Local/vcpkg-latest"
    buildResult:
      variable: "CMAKE_HAVE_PTHREAD_CREATE"
      cached: true
      stdout: |
        Change Dir: 'C:/Code/scoop/Build/msvc-debug/CMakeFiles/CMakeScratch/TryCompile-z6njw8'
        
        Run Build Command(s): C:/Users/<USER>/OneDrive/link/ninja.exe -v cmTC_fc229
        [1/2] C:\\PROGRA~2\\MICROS~2\\2019\\PROFES~1\\VC\\Tools\\MSVC\\1429~1.301\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP   /DWIN32 /D_WINDOWS /W3 /GR /EHsc -DCHECK_FUNCTION_EXISTS=pthread_create /MDd /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /showIncludes /FoCMakeFiles\\cmTC_fc229.dir\\CheckFunctionExists.cxx.obj /FdCMakeFiles\\cmTC_fc229.dir\\ /FS -c C:\\Code\\scoop\\Build\\msvc-debug\\CMakeFiles\\CMakeScratch\\TryCompile-z6njw8\\CheckFunctionExists.cxx
        [2/2] C:\\WINDOWS\\system32\\cmd.exe /C "cd . && C:\\Users\\<USER>\\OneDrive\\app\\cmake\\bin\\cmake.exe -E vs_link_exe --intdir=CMakeFiles\\cmTC_fc229.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100190~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100190~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~2\\MICROS~2\\2019\\PROFES~1\\VC\\Tools\\MSVC\\1429~1.301\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_fc229.dir\\CheckFunctionExists.cxx.obj  /out:cmTC_fc229.exe /implib:cmTC_fc229.lib /pdb:cmTC_fc229.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  pthread.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        FAILED: cmTC_fc229.exe 
        C:\\WINDOWS\\system32\\cmd.exe /C "cd . && C:\\Users\\<USER>\\OneDrive\\app\\cmake\\bin\\cmake.exe -E vs_link_exe --intdir=CMakeFiles\\cmTC_fc229.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100190~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100190~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~2\\MICROS~2\\2019\\PROFES~1\\VC\\Tools\\MSVC\\1429~1.301\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_fc229.dir\\CheckFunctionExists.cxx.obj  /out:cmTC_fc229.exe /implib:cmTC_fc229.lib /pdb:cmTC_fc229.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  pthread.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        LINK Pass 1: command "C:\\PROGRA~2\\MICROS~2\\2019\\PROFES~1\\VC\\Tools\\MSVC\\1429~1.301\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_fc229.dir\\CheckFunctionExists.cxx.obj /out:cmTC_fc229.exe /implib:cmTC_fc229.lib /pdb:cmTC_fc229.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console pthread.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTFILE:CMakeFiles\\cmTC_fc229.dir/intermediate.manifest CMakeFiles\\cmTC_fc229.dir/manifest.res" failed (exit code 1104) with the following output:
        LINK : fatal error LNK1104: cannot open file 'pthread.lib'\x0d
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
...
