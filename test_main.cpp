#include <iostream>
#include <string>
#include <filesystem>

// Simple test without external dependencies
int main() {
    std::cout << "sco CLI framework test\n";
    std::cout << "Version: 0.1.0\n\n";

    // Test filesystem operations
    std::cout << "Testing filesystem operations...\n";

    auto current_path = std::filesystem::current_path();
    std::cout << "Current directory: " << current_path.string() << "\n";

    // Test path operations
    auto test_path = current_path / "test_scoop";
    std::cout << "Test scoop path: " << test_path.string() << "\n";

    // Test directory creation
    try {
        if (!std::filesystem::exists(test_path)) {
            std::filesystem::create_directory(test_path);
            std::cout << "Created test directory\n";
        } else {
            std::cout << "Test directory already exists\n";
        }

        // Test subdirectories
        auto apps_dir = test_path / "apps";
        auto cache_dir = test_path / "cache";
        auto buckets_dir = test_path / "buckets";

        std::filesystem::create_directories(apps_dir);
        std::filesystem::create_directories(cache_dir);
        std::filesystem::create_directories(buckets_dir);

        std::cout << "Created subdirectories:\n";
        std::cout << "  " << apps_dir.string() << "\n";
        std::cout << "  " << cache_dir.string() << "\n";
        std::cout << "  " << buckets_dir.string() << "\n";

        // Clean up
        std::filesystem::remove_all(test_path);
        std::cout << "Cleaned up test directory\n";

    } catch (const std::exception& e) {
        std::cout << "Error: " << e.what() << "\n";
        return 1;
    }

    std::cout << "\nBasic functionality test passed!\n";
    std::cout << "\nTo test the full CLI framework, compile with dependencies:\n";
    std::cout << "  CLI11, nlohmann_json, spdlog\n";

    return 0;
}
