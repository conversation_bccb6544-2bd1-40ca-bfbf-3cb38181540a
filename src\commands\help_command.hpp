#pragma once

#include "base_command.hpp"
#include <CLI/CLI.hpp>
#include <iostream>
#include <spdlog/spdlog.h>

namespace sco {

class HelpCommand : public BaseCommand {
public:
    HelpCommand() = default;
    
    int execute() override {
        if (command_name_.empty()) {
            // Show general help
            SPDLOG_INFO("Usage: sco <command> [<args>]");
            SPDLOG_INFO("");
            SPDLOG_INFO("Available commands are listed below.");
            SPDLOG_INFO("");
            SPDLOG_INFO("Type 'sco help <command>' to get more help for a specific command.");
            SPDLOG_INFO("");

            SPDLOG_INFO("Command    Summary");
            SPDLOG_INFO("-------    -------");
            SPDLOG_INFO("alias      Manage scoop aliases");
            SPDLOG_INFO("bucket     Manage Scoop buckets");
            SPDLOG_INFO("cache      Show or clear the download cache");
            SPDLOG_INFO("cat        Show content of specified manifest");
            SPDLOG_INFO("checkup    Check for potential problems");
            SPDLOG_INFO("cleanup    Cleanup apps by removing old versions");
            SPDLOG_INFO("config     Get or set configuration values");
            SPDLOG_INFO("create     Create a custom app manifest");
            SPDLOG_INFO("depends    List dependencies for an app, in the order they'll be installed");
            SPDLOG_INFO("download   Download apps in the cache folder and verify hashes");
            SPDLOG_INFO("export     Exports installed apps, buckets (and optionally configs) in JSON format");
            SPDLOG_INFO("help       Show help for a command");
            SPDLOG_INFO("hold       Hold an app to disable updates");
            SPDLOG_INFO("home       Opens the app homepage");
            SPDLOG_INFO("import     Imports apps, buckets and configs from a Scoopfile in JSON format");
            SPDLOG_INFO("info       Display information about an app");
            SPDLOG_INFO("install    Install apps");
            SPDLOG_INFO("list       List installed apps");
            SPDLOG_INFO("prefix     Returns the path to the specified app");
            SPDLOG_INFO("reset      Reset an app to resolve conflicts");
            SPDLOG_INFO("search     Search available apps");
            SPDLOG_INFO("shim       Manipulate Scoop shims");
            SPDLOG_INFO("status     Show status and check for new app versions");
            SPDLOG_INFO("unhold     Unhold an app to enable updates");
            SPDLOG_INFO("uninstall  Uninstall an app");
            SPDLOG_INFO("update     Update apps, or Scoop itself");
            SPDLOG_INFO("virustotal Look for app's hash or url on virustotal.com");
            SPDLOG_INFO("which      Locate a shim/executable (similar to 'which' on Linux)");

            return 0;
        } else {
            // Show help for specific command
            if (app_) {
                try {
                    auto* subcommand = app_->get_subcommand(command_name_);
                    if (subcommand) {
                        SPDLOG_INFO("{}", subcommand->help());
                        return 0;
                    }
                } catch (const std::exception&) {
                    // Command not found
                }
            }

            SPDLOG_ERROR("Unknown command: {}", command_name_);
            SPDLOG_INFO("Type 'sco help' to see available commands.");
            return 1;
        }
    }
    
    std::string get_name() const override { return "help"; }
    std::string get_description() const override { return "Show help for a command"; }
    
    void set_command_name(const std::string& name) { command_name_ = name; }
    void set_app(CLI::App* app) { app_ = app; }
    
private:
    std::string command_name_;
    CLI::App* app_ = nullptr;
};

} // namespace sco
