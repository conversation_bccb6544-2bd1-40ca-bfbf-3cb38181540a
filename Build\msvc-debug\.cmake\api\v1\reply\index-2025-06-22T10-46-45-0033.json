{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "C:/Users/<USER>/OneDrive/app/cmake/bin/cmake.exe", "cpack": "C:/Users/<USER>/OneDrive/app/cmake/bin/cpack.exe", "ctest": "C:/Users/<USER>/OneDrive/app/cmake/bin/ctest.exe", "root": "C:/Users/<USER>/OneDrive/app/cmake/share/cmake-3.30"}, "version": {"isDirty": false, "major": 3, "minor": 30, "patch": 1, "string": "3.30.1", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-2c9eb0f4f3d07240cf2e.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "cache-v2-bae4e3bcfe6c00cea2f9.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-82fdbe4225fa8ea90f66.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, {"jsonFile": "toolchains-v1-cb7f191861ef5a82ab4f.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"client-vscode": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}, {"kind": "cmakeFiles", "version": 1}], "responses": [{"jsonFile": "cache-v2-bae4e3bcfe6c00cea2f9.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "codemodel-v2-2c9eb0f4f3d07240cf2e.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "toolchains-v1-cb7f191861ef5a82ab4f.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-82fdbe4225fa8ea90f66.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}]}}}}