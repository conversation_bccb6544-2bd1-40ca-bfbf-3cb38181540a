#include "config.h"
#include <fstream>
#include <spdlog/spdlog.h>
#include <windows.h>

namespace sco {

Config& Config::instance() {
    static Config instance;
    return instance;
}

void Config::load() {
    auto config_file = get_config_file();
    
    if (!std::filesystem::exists(config_file)) {
        set_defaults();
        save();
        return;
    }
    
    try {
        std::ifstream file(config_file);
        if (file.is_open()) {
            file >> config_data_;
            spdlog::debug("Configuration loaded from: {}", config_file.string());
        }
    } catch (const std::exception& e) {
        spdlog::warn("Failed to load configuration: {}", e.what());
        set_defaults();
    }
}

void Config::save() {
    auto config_file = get_config_file();
    
    try {
        // Ensure directory exists
        std::filesystem::create_directories(config_file.parent_path());
        
        std::ofstream file(config_file);
        if (file.is_open()) {
            file << config_data_.dump(2);
            spdlog::debug("Configuration saved to: {}", config_file.string());
        }
    } catch (const std::exception& e) {
        spdlog::error("Failed to save configuration: {}", e.what());
    }
}

std::string Config::get(const std::string& key, const std::string& default_value) const {
    if (config_data_.contains(key)) {
        return config_data_[key].get<std::string>();
    }
    return default_value;
}

bool Config::get_bool(const std::string& key, bool default_value) const {
    if (config_data_.contains(key)) {
        return config_data_[key].get<bool>();
    }
    return default_value;
}

int Config::get_int(const std::string& key, int default_value) const {
    if (config_data_.contains(key)) {
        return config_data_[key].get<int>();
    }
    return default_value;
}

void Config::set(const std::string& key, const std::string& value) {
    config_data_[key] = value;
}

void Config::set_bool(const std::string& key, bool value) {
    config_data_[key] = value;
}

void Config::set_int(const std::string& key, int value) {
    config_data_[key] = value;
}

std::filesystem::path Config::get_scoop_dir() const {
    if (global_mode_) {
        return get_program_data() / "scoop";
    } else {
        return get_user_profile() / "scoop";
    }
}

std::filesystem::path Config::get_apps_dir() const {
    return get_scoop_dir() / "apps";
}

std::filesystem::path Config::get_cache_dir() const {
    return get_scoop_dir() / "cache";
}

std::filesystem::path Config::get_buckets_dir() const {
    return get_scoop_dir() / "buckets";
}

std::filesystem::path Config::get_shims_dir() const {
    return get_scoop_dir() / "shims";
}

std::filesystem::path Config::get_config_file() const {
    return get_scoop_dir() / "config.json";
}

void Config::set_defaults() {
    config_data_ = nlohmann::json{
        {"aria2-enabled", true},
        {"aria2-warning-enabled", true},
        {"aria2-retry-wait", 2},
        {"aria2-split", 5},
        {"aria2-max-connection-per-server", 5},
        {"aria2-min-split-size", "5M"},
        {"aria2-options", ""},
        {"debug", false},
        {"force_update", false},
        {"show_update_log", true},
        {"scoop_repo", "https://github.com/ScoopInstaller/Scoop"},
        {"scoop_branch", "master"}
    };
}

std::filesystem::path Config::get_user_profile() const {
    char* userprofile = nullptr;
    size_t len = 0;
    if (_dupenv_s(&userprofile, &len, "USERPROFILE") == 0 && userprofile != nullptr) {
        std::filesystem::path path(userprofile);
        free(userprofile);
        return path;
    }
    return std::filesystem::current_path();
}

std::filesystem::path Config::get_program_data() const {
    char* programdata = nullptr;
    size_t len = 0;
    if (_dupenv_s(&programdata, &len, "PROGRAMDATA") == 0 && programdata != nullptr) {
        std::filesystem::path path(programdata);
        free(programdata);
        return path;
    }
    return std::filesystem::path("C:\\ProgramData");
}

} // namespace sco
