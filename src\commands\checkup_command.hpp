#pragma once

#include "base_command.hpp"
#include "../core/config.hpp"
#include <iostream>
#include <filesystem>
#include <spdlog/spdlog.h>
#include <windows.h>

namespace sco {

class CheckupCommand : public BaseCommand {
public:
    CheckupCommand() = default;
    
    int execute() override {
        std::cout << "Checking system for potential problems...\n\n";
        
        int issues = 0;
        issues += check_scoop_directories();
        issues += check_environment_variables();
        issues += check_permissions();
        issues += check_powershell_execution_policy();
        issues += check_long_path_support();
        
        std::cout << "\n";
        if (issues == 0) {
            std::cout << "✓ No issues found.\n";
        } else {
            std::cout << "⚠ Found " << issues << " potential issue(s).\n";
        }
        
        return issues > 0 ? 1 : 0;
    }
    
    std::string get_name() const override { return "checkup"; }
    std::string get_description() const override { return "Check for potential problems"; }
    
private:
    int check_scoop_directories() {
        std::cout << "Checking Scoop directories...\n";
        int issues = 0;
        
        auto& config = Config::instance();
        config.load();
        
        auto scoop_dir = config.get_scoop_dir();
        auto apps_dir = config.get_apps_dir();
        auto cache_dir = config.get_cache_dir();
        auto buckets_dir = config.get_buckets_dir();
        auto shims_dir = config.get_shims_dir();
        
        // Check if main scoop directory exists
        if (!std::filesystem::exists(scoop_dir)) {
            std::cout << "  ✗ Scoop directory does not exist: " << scoop_dir.string() << "\n";
            std::cout << "    Run 'sco install <app>' to initialize Scoop.\n";
            issues++;
        } else {
            std::cout << "  ✓ Scoop directory exists: " << scoop_dir.string() << "\n";
        }
        
        // Check subdirectories
        std::vector<std::pair<std::filesystem::path, std::string>> dirs = {
            {apps_dir, "Apps directory"},
            {cache_dir, "Cache directory"},
            {buckets_dir, "Buckets directory"},
            {shims_dir, "Shims directory"}
        };
        
        for (const auto& [dir, name] : dirs) {
            if (std::filesystem::exists(scoop_dir) && !std::filesystem::exists(dir)) {
                std::cout << "  ⚠ " << name << " missing: " << dir.string() << "\n";
                try {
                    std::filesystem::create_directories(dir);
                    std::cout << "    Created directory.\n";
                } catch (const std::exception& e) {
                    std::cout << "    Failed to create directory: " << e.what() << "\n";
                    issues++;
                }
            }
        }
        
        return issues;
    }
    
    int check_environment_variables() {
        std::cout << "\nChecking environment variables...\n";
        int issues = 0;
        
        auto& config = Config::instance();
        auto shims_dir = config.get_shims_dir();
        
        // Check if shims directory is in PATH
        char* path_env = nullptr;
        size_t len = 0;
        if (_dupenv_s(&path_env, &len, "PATH") == 0 && path_env != nullptr) {
            std::string path_str(path_env);
            free(path_env);
            
            if (path_str.find(shims_dir.string()) == std::string::npos) {
                std::cout << "  ⚠ Shims directory not in PATH: " << shims_dir.string() << "\n";
                std::cout << "    Add this directory to your PATH environment variable.\n";
                issues++;
            } else {
                std::cout << "  ✓ Shims directory is in PATH\n";
            }
        } else {
            std::cout << "  ✗ Could not read PATH environment variable\n";
            issues++;
        }
        
        return issues;
    }
    
    int check_permissions() {
        std::cout << "\nChecking permissions...\n";
        int issues = 0;
        
        auto& config = Config::instance();
        auto scoop_dir = config.get_scoop_dir();
        
        if (std::filesystem::exists(scoop_dir)) {
            // Try to create a test file
            auto test_file = scoop_dir / "test_permissions.tmp";
            try {
                std::ofstream file(test_file);
                if (file.is_open()) {
                    file << "test";
                    file.close();
                    std::filesystem::remove(test_file);
                    std::cout << "  ✓ Write permissions OK\n";
                } else {
                    std::cout << "  ✗ No write permissions to Scoop directory\n";
                    issues++;
                }
            } catch (const std::exception& e) {
                std::cout << "  ✗ Permission check failed: " << e.what() << "\n";
                issues++;
            }
        }
        
        return issues;
    }
    
    int check_powershell_execution_policy() {
        std::cout << "\nChecking PowerShell execution policy...\n";
        int issues = 0;
        
        // This is a placeholder - in a real implementation, we would check
        // PowerShell execution policy using Windows APIs or by running PowerShell
        std::cout << "  ℹ PowerShell execution policy check not implemented\n";
        std::cout << "    Ensure execution policy allows running scripts:\n";
        std::cout << "    Set-ExecutionPolicy RemoteSigned -Scope CurrentUser\n";
        
        return issues;
    }
    
    int check_long_path_support() {
        std::cout << "\nChecking long path support...\n";
        int issues = 0;
        
        // Check Windows version and long path support
        std::cout << "  ℹ Long path support check not fully implemented\n";
        std::cout << "    For Windows 10 version 1607+, enable long path support:\n";
        std::cout << "    Computer Configuration > Administrative Templates > System > Filesystem\n";
        std::cout << "    Enable 'Enable Win32 long paths'\n";
        
        return issues;
    }
};

} // namespace sco
